import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { EditUserComponent } from './edit-user.component';

describe('EditUserComponent', () => {
  let component: EditUserComponent;
  let fixture: ComponentFixture<EditUserComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ EditUserComponent ],
      imports: [IonicModule.forRoot()]
    }).compileComponents();

    fixture = TestBed.createComponent(EditUserComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Test to verify approval filtering logic
  it('should filter approvals correctly based on agent type', () => {
    // Mock approval data
    const mockApprovals = [
      { APPR_TYPE: 'INTERNAL_ONLY', SCOPE: 'I', DESCRIPTION: 'Internal Only Approval' },
      { APPR_TYPE: 'EXTERNAL_ONLY', SCOPE: 'E', DESCRIPTION: 'External Only Approval' },
      { APPR_TYPE: 'BOTH_SCOPES', SCOPE: 'B', DESCRIPTION: 'Both Scopes Approval' }
    ];

    // Test filtering logic for internal agent
    const internalFiltered = mockApprovals.filter(approval => {
      const isAgentInternal = true; // Simulating internal agent
      if (approval.SCOPE === 'B') {
        return true;
      } else if (isAgentInternal && approval.SCOPE === 'I') {
        return true;
      } else if (!isAgentInternal && approval.SCOPE === 'E') {
        return true;
      }
      return false;
    });

    expect(internalFiltered.length).toBe(2); // Should include 'I' and 'B' scopes
    expect(internalFiltered.some(a => a.SCOPE === 'I')).toBeTruthy();
    expect(internalFiltered.some(a => a.SCOPE === 'B')).toBeTruthy();
    expect(internalFiltered.some(a => a.SCOPE === 'E')).toBeFalsy();

    // Test filtering logic for external agent
    const externalFiltered = mockApprovals.filter(approval => {
      const isAgentInternal = false; // Simulating external agent
      if (approval.SCOPE === 'B') {
        return true;
      } else if (isAgentInternal && approval.SCOPE === 'I') {
        return true;
      } else if (!isAgentInternal && approval.SCOPE === 'E') {
        return true;
      }
      return false;
    });

    expect(externalFiltered.length).toBe(2); // Should include 'E' and 'B' scopes
    expect(externalFiltered.some(a => a.SCOPE === 'E')).toBeTruthy();
    expect(externalFiltered.some(a => a.SCOPE === 'B')).toBeTruthy();
    expect(externalFiltered.some(a => a.SCOPE === 'I')).toBeFalsy();
  });
});
